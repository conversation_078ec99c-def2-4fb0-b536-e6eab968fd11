{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "sass": "sass --watch ./src/index.scss:./src/style/style.css"}, "dependencies": {"axios": "^1.9.0", "bootstrap": "^5.3.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1"}, "devDependencies": {"@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@typescript-eslint/eslint-plugin": "^5.61.0", "@typescript-eslint/parser": "^5.61.0", "@vitejs/plugin-react": "^4.0.1", "eslint": "^8.44.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "sass": "^1.63.6", "typescript": "^5.0.2", "vite": "^4.4.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}