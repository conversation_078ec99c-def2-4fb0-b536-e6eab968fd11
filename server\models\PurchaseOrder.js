import mongoose from 'mongoose';

const purchaseOrderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    unique: true,
    trim: true
  },
  client: {
    type: String,
    required: true,
    trim: true
  },
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: false,
    trim: true
  },
  pickupLocation: {
    type: String,
    required: true,
    enum: ['1605 Renaissance Commons Blvd., Boynton Beach, FL 33426']
  },
  deliveryLocation: {
    type: String,
    required: true,
    enum: [
      '1500 Gateway Blvd., Boynton Beach, FL 33426',
      '2000 Corporate Blvd., Boca Raton, FL 33431',
      '1800 Congress Ave., Delray Beach, FL 33445',
      '2200 Glades Rd., Boca Raton, FL 33431',
      '1400 Town Center Cir., Boca Raton, FL 33486',
      '3000 Yamato Rd., Boca Raton, FL 33434'
    ]
  },
  pickupDate: {
    type: Date,
    required: true
  },
  deliveryDate: {
    type: Date,
    required: true
  },
  weight: {
    type: Number,
    required: true,
    min: 0
  },
  haulingRate: {
    type: Number,
    required: true,
    min: 0
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'in_transit', 'delivered', 'cancelled'],
    default: 'pending'
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assignedTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  acceptedAt: {
    type: Date,
    default: null
  },
  notes: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Generate order number before saving
purchaseOrderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    try {
      // Get the count of existing documents
      const count = await this.constructor.countDocuments();
      this.orderNumber = `PO-${String(count + 1).padStart(6, '0')}`;
      console.log('Generated order number:', this.orderNumber);
    } catch (error) {
      console.error('Error generating order number:', error);
      return next(error);
    }
  }
  next();
});

// Update acceptedAt when status changes to accepted
purchaseOrderSchema.pre('save', function(next) {
  if (this.isModified('status') && this.status === 'accepted' && !this.acceptedAt) {
    this.acceptedAt = new Date();
  }
  next();
});

const PurchaseOrder = mongoose.model('PurchaseOrder', purchaseOrderSchema);

export default PurchaseOrder;
