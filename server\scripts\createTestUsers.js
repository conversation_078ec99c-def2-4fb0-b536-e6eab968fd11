import mongoose from 'mongoose';
import User from '../models/User.js';
import 'dotenv/config';

const connectionString = process.env.ATLAS_URI || '';

const testUsers = [
  {
    username: 'admin1',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    firstName: '<PERSON>',
    lastName: 'Admin'
  },
  {
    username: 'trucker1',
    email: '<EMAIL>',
    password: 'trucker123',
    role: 'trucker',
    firstName: 'Mike',
    lastName: 'Driver'
  },
  {
    username: 'trucker2',
    email: '<EMAIL>',
    password: 'trucker123',
    role: 'trucker',
    firstName: 'Sarah',
    lastName: '<PERSON>'
  }
];

async function createTestUsers() {
  try {
    // Connect to MongoDB
    await mongoose.connect(connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('Connected to MongoDB');

    // Clear existing users (optional - remove this in production)
    await User.deleteMany({});
    console.log('Cleared existing users');

    // Create test users
    for (const userData of testUsers) {
      const existingUser = await User.findOne({ 
        $or: [{ email: userData.email }, { username: userData.username }] 
      });

      if (!existingUser) {
        const user = new User(userData);
        await user.save();
        console.log(`Created user: ${userData.username} (${userData.role})`);
      } else {
        console.log(`User ${userData.username} already exists`);
      }
    }

    console.log('Test users created successfully!');
    console.log('\nLogin credentials:');
    console.log('Admin: username=admin1, password=admin123');
    console.log('Trucker 1: username=trucker1, password=trucker123');
    console.log('Trucker 2: username=trucker2, password=trucker123');

  } catch (error) {
    console.error('Error creating test users:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

createTestUsers();
