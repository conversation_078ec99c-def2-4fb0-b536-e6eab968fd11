/* Bootstrap overrides and custom styles */
body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom application styles */
#root {
  height: 100vh;
}

/* Override any conflicting Bootstrap styles if needed */
.navbar-brand {
  font-weight: bold;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn {
  font-weight: 500;
}

/* Loading spinner styles */
.spinner-border {
  width: 2rem;
  height: 2rem;
}
