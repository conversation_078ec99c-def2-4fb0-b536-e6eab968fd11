import React, { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';
import axios from 'axios';

interface DeliveryLocation {
  _id: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  status: 'pending' | 'completed';
  completedAt?: string;
  deliveryPhoto?: string;
  signature?: string;
}

interface PurchaseOrder {
  _id: string;
  orderNumber: string;
  client: string;
  title: string;
  description?: string;
  pickupLocation: {
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  deliveryLocations: DeliveryLocation[];
  pickupDate: string;
  deliveryDate: string;
  weight: number;
  haulingRate: number;
  status: string;
  createdBy: {
    username: string;
    firstName: string;
    lastName: string;
  };
  assignedTo?: {
    username: string;
    firstName: string;
    lastName: string;
  };
  acceptedAt?: string;
  createdAt: string;
}

const AdminDashboard: React.FC = () => {
  const { user, logout } = useAuth();
  const [orders, setOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);

  // Get today's date in YYYY-MM-DD format
  const getTodayDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  const [formData, setFormData] = useState({
    client: '',
    title: '',
    description: '',
    material: '',
    pickupLocation: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
    deliveryLocations: [] as string[],
    pickupDate: getTodayDate(),
    deliveryDate: '',
    weight: '',
    haulingRate: '',
    notes: ''
  });

  const availableDeliveryLocations = [
    '1500 Gateway Blvd., Boynton Beach, FL 33426',
    '2000 Corporate Blvd., Boca Raton, FL 33431',
    '1800 Congress Ave., Delray Beach, FL 33445',
    '2200 Glades Rd., Boca Raton, FL 33431',
    '1400 Town Center Cir., Boca Raton, FL 33486',
    '3000 Yamato Rd., Boca Raton, FL 33434',
    'Dordrecht, Netherlands'
  ];

  const availablePickupLocations = [
    '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
    'Dordrecht, Netherlands'
  ];

  const materialTypes = [
    // Aggregates & Base Materials
    'Crushed Concrete',
    'Crushed Asphalt',
    'ABC (Aggregate Base Course)',
    'Road Base',
    '#57 Stone',
    '#89 Stone',
    'Rip Rap (6"–12")',
    'Pea Gravel',
    'Fill Rock',
    'Oversized Rock',
    // Sand & Soil Products
    'Fill Sand',
    'Mason Sand',
    'Concrete Sand',
    'Screened Sand',
    'Topsoil – Unscreened',
    'Topsoil – Screened',
    'Organic Topsoil',
    'Sandy Loam',
    'Clay – Red',
    'Clay – Gray',
    'Stabilized Sand',
    // Recycled / Specialty Materials
    'Recycled Asphalt Millings',
    'Recycled Concrete Base',
    'RAP (Reclaimed Asphalt Pavement)',
    'Flowable Fill (CLSM)',
    'Muck (Haul-off)',
    'Spoil Dirt',
    'Contaminated Soil',
    'Lime Rock Base',
    'Shell Rock',
    // Landscaping & Decorative
    'Mulch – Natural',
    'Mulch – Red',
    'Mulch – Brown',
    'Mulch – Black',
    'River Rock – Small',
    'River Rock – Medium',
    'River Rock – Large',
    'Lava Rock',
    'Pine Bark Nuggets',
    'Potting Soil Mix',
    // Dust & Fines
    'Rock Fines',
    'Granite Fines',
    'Limestone Dust',
    'Screening Material',
    // Bulk Liquids & Additives
    'Water (Dust Control)',
    'Liquid Lime',
    'Slurry Cement',
    // Haul-Off / Disposal Types
    'Concrete Demo (Broken)',
    'Asphalt Demo',
    'Mixed Construction Debris',
    'Tree Debris / Vegetation',
    'Excess Dirt – Clean',
    'Excess Dirt – Contaminated',
    'Septic Pump-Out'
  ];

  const API_BASE_URL = 'http://localhost:5055/api';

  useEffect(() => {
    fetchOrders();
  }, []);

  const fetchOrders = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/purchase-orders`);
      if (response.data.success) {
        setOrders(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrder = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.client || !formData.title || !formData.material || formData.deliveryLocations.length === 0 ||
        !formData.pickupDate || !formData.deliveryDate ||
        !formData.weight || !formData.haulingRate) {
      alert('Please fill in all required fields including material type and select at least one delivery location');
      return;
    }

    try {
      const orderData = {
        client: formData.client.trim(),
        title: formData.title.trim(),
        description: formData.description.trim(),
        pickupLocation: formData.pickupLocation,
        deliveryLocations: formData.deliveryLocations,
        pickupDate: formData.pickupDate,
        deliveryDate: formData.deliveryDate,
        weight: parseFloat(formData.weight),
        haulingRate: parseFloat(formData.haulingRate),
        notes: formData.notes.trim()
      };

      console.log('Sending order data:', orderData);

      const response = await axios.post(`${API_BASE_URL}/purchase-orders`, orderData);
      console.log('Response:', response.data);

      if (response.data.success) {
        setOrders([response.data.data, ...orders]);
        setShowCreateForm(false);
        // Reset form
        setFormData({
          client: '',
          title: '',
          description: '',
          material: '',
          pickupLocation: '1605 Renaissance Commons Blvd., Boynton Beach, FL 33426',
          deliveryLocations: [],
          pickupDate: getTodayDate(),
          deliveryDate: '',
          weight: '',
          haulingRate: '',
          notes: ''
        });
        alert('Purchase order created successfully!');
      }
    } catch (error: any) {
      console.error('Error creating order:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error creating purchase order: ${errorMessage}`);
    }
  };

  const handleDeleteAllOrders = async () => {
    if (!window.confirm('Are you sure you want to delete ALL purchase orders? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await axios.delete(`${API_BASE_URL}/purchase-orders/admin/delete-all`);

      if (response.data.success) {
        setOrders([]);
        alert(`Successfully deleted ${response.data.deletedCount} purchase orders`);
      }
    } catch (error: any) {
      console.error('Error deleting all orders:', error);
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred';
      alert(`Error deleting orders: ${errorMessage}`);
    }
  };

  const handleDeliveryLocationToggle = (location: string) => {
    const currentLocations = [...formData.deliveryLocations];
    const index = currentLocations.indexOf(location);

    if (index > -1) {
      currentLocations.splice(index, 1);
    } else {
      currentLocations.push(location);
    }

    setFormData({...formData, deliveryLocations: currentLocations});
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-warning';
      case 'accepted': return 'bg-success';
      case 'pickup_completed': return 'bg-info';
      case 'en_route': return 'bg-info';
      case 'delivery_in_progress': return 'bg-primary';
      case 'delivered': return 'bg-success';
      case 'cancelled': return 'bg-danger';
      default: return 'bg-secondary';
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid">
      {/* Header */}
      <nav className="navbar navbar-expand-lg navbar-dark bg-success mb-4">
        <div className="container-fluid">
          <span className="navbar-brand">
            <i className="bi bi-person-gear me-2"></i>
            Admin Dashboard
          </span>
          <div className="navbar-nav ms-auto">
            <span className="navbar-text me-3">
              Welcome, {user?.firstName} {user?.lastName}
            </span>
            <button className="btn btn-outline-light" onClick={logout}>
              <i className="bi bi-box-arrow-right me-1"></i>
              Logout
            </button>
          </div>
        </div>
      </nav>

      <div className="container-fluid">
        {/* Action Bar */}
        <div className="row mb-4">
          <div className="col">
            <div className="d-flex justify-content-between align-items-center">
              <h2>Purchase Orders</h2>
              <div>
                <button
                  className="btn btn-danger me-2"
                  onClick={handleDeleteAllOrders}
                  title="Delete all orders (for testing)"
                >
                  <i className="bi bi-trash me-2"></i>
                  Delete All Orders
                </button>
                <button
                  className="btn btn-success"
                  onClick={() => setShowCreateForm(true)}
                >
                  <i className="bi bi-plus-circle me-2"></i>
                  Create New Order
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics */}
        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card bg-warning text-white">
              <div className="card-body">
                <h5>Pending Orders</h5>
                <h3>{orders.filter(o => o.status === 'pending').length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-success text-white">
              <div className="card-body">
                <h5>Active Orders</h5>
                <h3>{orders.filter(o => ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'].includes(o.status)).length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-info text-white">
              <div className="card-body">
                <h5>In Progress</h5>
                <h3>{orders.filter(o => ['pickup_completed', 'en_route', 'delivery_in_progress'].includes(o.status)).length}</h3>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-primary text-white">
              <div className="card-body">
                <h5>Delivered</h5>
                <h3>{orders.filter(o => o.status === 'delivered').length}</h3>
              </div>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="card">
          <div className="card-body">
            <div className="table-responsive">
              <table className="table table-striped">
                <thead>
                  <tr>
                    <th>Order #</th>
                    <th>Client</th>
                    <th>Title</th>
                    <th>Pickup Location</th>
                    <th>Delivery Locations</th>
                    <th>Status</th>
                    <th>Assigned To</th>
                    <th>Hauling Rate</th>
                    <th>Created</th>
                  </tr>
                </thead>
                <tbody>
                  {orders.map((order) => (
                    <tr key={order._id}>
                      <td>{order.orderNumber}</td>
                      <td>{order.client}</td>
                      <td>{order.title}</td>
                      <td>{typeof order.pickupLocation === 'object' ? order.pickupLocation.address : order.pickupLocation}</td>
                      <td>
                        {order.deliveryLocations && order.deliveryLocations.length > 0 ? (
                          <div>
                            {order.deliveryLocations.map((location, index) => (
                              <div key={location._id || index} className="mb-1">
                                <small className={`badge ${location.status === 'completed' ? 'bg-success' : 'bg-secondary'} me-1`}>
                                  {location.status === 'completed' ? '✓' : '○'}
                                </small>
                                <small>{location.address}</small>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-muted">No delivery locations</span>
                        )}
                      </td>
                      <td>
                        <span className={`badge ${getStatusBadgeClass(order.status)}`}>
                          {order.status.replace('_', ' ').toUpperCase()}
                        </span>
                      </td>
                      <td>
                        {order.assignedTo ?
                          `${order.assignedTo.firstName} ${order.assignedTo.lastName}` :
                          'Unassigned'
                        }
                      </td>
                      <td>${order.haulingRate.toLocaleString()}</td>
                      <td>
                        <div>{new Date(order.createdAt).toLocaleDateString()}</div>
                        <small className="text-muted">{new Date(order.createdAt).toLocaleTimeString()}</small>
                      </td>
                    </tr>
                  ))}                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Create Order Modal */}
      {showCreateForm && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Create New Purchase Order</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowCreateForm(false)}
                ></button>
              </div>
              <form onSubmit={handleCreateOrder}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Client *</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.client}
                          onChange={(e) => setFormData({...formData, client: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Title *</label>
                        <input
                          type="text"
                          className="form-control"
                          value={formData.title}
                          onChange={(e) => setFormData({...formData, title: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <label className="form-label">Material Type *</label>
                    <select
                      className="form-control"
                      value={formData.material}
                      onChange={(e) => setFormData({...formData, material: e.target.value})}
                      required
                    >
                      <option value="">Select material type...</option>
                      <optgroup label="🏗️ Aggregates & Base Materials">
                        <option value="Crushed Concrete">Crushed Concrete</option>
                        <option value="Crushed Asphalt">Crushed Asphalt</option>
                        <option value="ABC (Aggregate Base Course)">ABC (Aggregate Base Course)</option>
                        <option value="Road Base">Road Base</option>
                        <option value="#57 Stone">#57 Stone</option>
                        <option value="#89 Stone">#89 Stone</option>
                        <option value="Rip Rap (6&quot;–12&quot;)">Rip Rap (6&quot;–12&quot;)</option>
                        <option value="Pea Gravel">Pea Gravel</option>
                        <option value="Fill Rock">Fill Rock</option>
                        <option value="Oversized Rock">Oversized Rock</option>
                      </optgroup>
                      <optgroup label="🏗️ Sand & Soil Products">
                        <option value="Fill Sand">Fill Sand</option>
                        <option value="Mason Sand">Mason Sand</option>
                        <option value="Concrete Sand">Concrete Sand</option>
                        <option value="Screened Sand">Screened Sand</option>
                        <option value="Topsoil – Unscreened">Topsoil – Unscreened</option>
                        <option value="Topsoil – Screened">Topsoil – Screened</option>
                        <option value="Organic Topsoil">Organic Topsoil</option>
                        <option value="Sandy Loam">Sandy Loam</option>
                        <option value="Clay – Red">Clay – Red</option>
                        <option value="Clay – Gray">Clay – Gray</option>
                        <option value="Stabilized Sand">Stabilized Sand</option>
                      </optgroup>
                      <optgroup label="♻️ Recycled / Specialty Materials">
                        <option value="Recycled Asphalt Millings">Recycled Asphalt Millings</option>
                        <option value="Recycled Concrete Base">Recycled Concrete Base</option>
                        <option value="RAP (Reclaimed Asphalt Pavement)">RAP (Reclaimed Asphalt Pavement)</option>
                        <option value="Flowable Fill (CLSM)">Flowable Fill (CLSM)</option>
                        <option value="Muck (Haul-off)">Muck (Haul-off)</option>
                        <option value="Spoil Dirt">Spoil Dirt</option>
                        <option value="Contaminated Soil">Contaminated Soil</option>
                        <option value="Lime Rock Base">Lime Rock Base</option>
                        <option value="Shell Rock">Shell Rock</option>
                      </optgroup>
                      <optgroup label="🌴 Landscaping & Decorative">
                        <option value="Mulch – Natural">Mulch – Natural</option>
                        <option value="Mulch – Red">Mulch – Red</option>
                        <option value="Mulch – Brown">Mulch – Brown</option>
                        <option value="Mulch – Black">Mulch – Black</option>
                        <option value="River Rock – Small">River Rock – Small</option>
                        <option value="River Rock – Medium">River Rock – Medium</option>
                        <option value="River Rock – Large">River Rock – Large</option>
                        <option value="Lava Rock">Lava Rock</option>
                        <option value="Pine Bark Nuggets">Pine Bark Nuggets</option>
                        <option value="Potting Soil Mix">Potting Soil Mix</option>
                      </optgroup>
                      <optgroup label="🌫️ Dust & Fines">
                        <option value="Rock Fines">Rock Fines</option>
                        <option value="Granite Fines">Granite Fines</option>
                        <option value="Limestone Dust">Limestone Dust</option>
                        <option value="Screening Material">Screening Material</option>
                      </optgroup>
                      <optgroup label="💧 Bulk Liquids & Additives">
                        <option value="Water (Dust Control)">Water (Dust Control)</option>
                        <option value="Liquid Lime">Liquid Lime</option>
                        <option value="Slurry Cement">Slurry Cement</option>
                      </optgroup>
                      <optgroup label="🗑️ Haul-Off / Disposal Types">
                        <option value="Concrete Demo (Broken)">Concrete Demo (Broken)</option>
                        <option value="Asphalt Demo">Asphalt Demo</option>
                        <option value="Mixed Construction Debris">Mixed Construction Debris</option>
                        <option value="Tree Debris / Vegetation">Tree Debris / Vegetation</option>
                        <option value="Excess Dirt – Clean">Excess Dirt – Clean</option>
                        <option value="Excess Dirt – Contaminated">Excess Dirt – Contaminated</option>
                        <option value="Septic Pump-Out">Septic Pump-Out</option>
                      </optgroup>
                    </select>
                  </div>

                  <div className="mb-3">
                    <label className="form-label">Description</label>
                    <textarea
                      className="form-control"
                      rows={3}
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                    ></textarea>
                  </div>

                  <div className="row">
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Pickup Location *</label>
                        <select
                          className="form-control"
                          value={formData.pickupLocation}
                          onChange={(e) => setFormData({...formData, pickupLocation: e.target.value})}
                          required
                        >
                          <option value="1605 Renaissance Commons Blvd., Boynton Beach, FL 33426">
                            1605 Renaissance Commons Blvd., Boynton Beach, FL 33426
                          </option>
                          <option value="Dordrecht, Netherlands">Dordrecht, Netherlands</option>  {/* Add this */}
                        </select>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="mb-3">
                        <label className="form-label">Delivery Locations * (Select multiple)</label>
                        <div className="border rounded p-2" style={{ maxHeight: '150px', overflowY: 'auto' }}>
                          {availableDeliveryLocations.map((location) => (
                            <div key={location} className="form-check">
                              <input
                                className="form-check-input"
                                type="checkbox"
                                id={`delivery-${location}`}
                                checked={formData.deliveryLocations.includes(location)}
                                onChange={() => handleDeliveryLocationToggle(location)}
                              />
                              <label className="form-check-label" htmlFor={`delivery-${location}`}>
                                <small>{location}</small>
                              </label>
                            </div>
                          ))}
                        </div>
                        {formData.deliveryLocations.length > 0 && (
                          <small className="text-muted">
                            Selected: {formData.deliveryLocations.length} location(s)
                          </small>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="row">
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Pickup Date *</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.pickupDate}
                          onChange={(e) => setFormData({...formData, pickupDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Delivery Date *</label>
                        <input
                          type="date"
                          className="form-control"
                          value={formData.deliveryDate}
                          onChange={(e) => setFormData({...formData, deliveryDate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Weight (lbs) *</label>
                        <input
                          type="number"
                          className="form-control"
                          value={formData.weight}
                          onChange={(e) => setFormData({...formData, weight: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-3">
                      <div className="mb-3">
                        <label className="form-label">Hauling Rate ($) *</label>
                        <input
                          type="number"
                          step="0.01"
                          className="form-control"
                          value={formData.haulingRate}
                          onChange={(e) => setFormData({...formData, haulingRate: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowCreateForm(false)}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="btn btn-success">
                    Create Order
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminDashboard;
