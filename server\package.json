{"name": "server", "type": "module", "version": "1.0.0", "main": "server.js", "author": "To<PERSON>", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "mongodb": "^5.7.0", "mongoose": "^7.3.4", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3"}, "devDependencies": {"nodemon": "^3.0.1"}, "scripts": {"dev": "nodemon server.mjs", "create-test-users": "node scripts/createTestUsers.js"}}