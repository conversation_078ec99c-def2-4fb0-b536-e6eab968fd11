import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider, useAuth } from '../context/AuthContext';
import LoginScreen from '../components/LoginScreen';
import TruckerLogin from '../components/TruckerLogin';
import AdminLogin from '../components/AdminLogin';
import TruckerDashboard from '../components/TruckerDashboard';
import AdminDashboard from '../components/AdminDashboard';
import ProtectedRoute from '../components/ProtectedRoute';

const AppRoutes: React.FC = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center vh-100">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/"
        element={
          user ? (
            <Navigate to={user.role === 'admin' ? '/admin/dashboard' : '/trucker/dashboard'} replace />
          ) : (
            <LoginScreen />
          )
        }
      />
      <Route
        path="/login/trucker"
        element={
          user ? (
            <Navigate to={user.role === 'admin' ? '/admin/dashboard' : '/trucker/dashboard'} replace />
          ) : (
            <TruckerLogin />
          )
        }
      />
      <Route
        path="/login/admin"
        element={
          user ? (
            <Navigate to={user.role === 'admin' ? '/admin/dashboard' : '/trucker/dashboard'} replace />
          ) : (
            <AdminLogin />
          )
        }
      />

      {/* Protected routes */}
      <Route
        path="/trucker/dashboard"
        element={
          <ProtectedRoute requiredRole="trucker">
            <TruckerDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/admin/dashboard"
        element={
          <ProtectedRoute requiredRole="admin">
            <AdminDashboard />
          </ProtectedRoute>
        }
      />

      {/* Catch all route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <AppRoutes />
      </Router>
    </AuthProvider>
  );
}

export default App;
