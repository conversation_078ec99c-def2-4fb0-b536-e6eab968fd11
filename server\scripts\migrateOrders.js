import mongoose from 'mongoose';
import 'dotenv/config';
import PurchaseOrder, { locationCoordinates } from '../models/PurchaseOrder.js';

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.ATLAS_URI);
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Migration function to convert old schema to new schema
const migrateOrders = async () => {
  try {
    console.log('Starting migration...');
    
    // Find all orders that need migration (have old deliveryLocation field)
    const ordersToMigrate = await PurchaseOrder.find({
      deliveryLocation: { $exists: true }
    });

    console.log(`Found ${ordersToMigrate.length} orders to migrate`);

    for (const order of ordersToMigrate) {
      console.log(`Migrating order ${order.orderNumber}...`);
      
      // Convert pickupLocation to new format
      if (typeof order.pickupLocation === 'string') {
        const coords = locationCoordinates[order.pickupLocation];
        order.pickupLocation = {
          address: order.pickupLocation,
          coordinates: coords || { lat: 0, lng: 0 }
        };
      }

      // Convert deliveryLocation to deliveryLocations array
      if (order.deliveryLocation && !order.deliveryLocations) {
        const coords = locationCoordinates[order.deliveryLocation];
        order.deliveryLocations = [{
          address: order.deliveryLocation,
          coordinates: coords || { lat: 0, lng: 0 },
          status: 'pending'
        }];
        
        // Remove old deliveryLocation field
        order.deliveryLocation = undefined;
      }

      await order.save();
      console.log(`✓ Migrated order ${order.orderNumber}`);
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration error:', error);
  }
};

// Run migration
const runMigration = async () => {
  await connectDB();
  await migrateOrders();
  await mongoose.connection.close();
  console.log('Database connection closed');
};

runMigration();
