import express from 'express';
import PurchaseOrder from '../models/PurchaseOrder.js';
import { authenticateToken, requireAdmin, requireTrucker, requireAdminOrTrucker } from '../middleware/auth.js';

const router = express.Router();

// Get all purchase orders (admin can see all, truckers see only pending and their assigned)
router.get('/', authenticateToken, async (req, res) => {
  try {
    let query = {};

    if (req.user.role === 'trucker') {
      // Check if trucker has an active order (accepted but not delivered)
      const activeOrder = await PurchaseOrder.findOne({
        assignedTo: req.user._id,
        status: { $in: ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'] }
      });

      if (activeOrder) {
        // If trucker has an active order, only show that order
        query = { _id: activeOrder._id };
      } else {
        // If no active order, show pending orders
        query = { status: 'pending' };
      }
    }
    // Admins can see all orders (no filter needed)

    const orders = await PurchaseOrder.find(query)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase orders',
      error: error.message
    });
  }
});

// Get single purchase order
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Check permissions
    if (req.user.role === 'trucker' && 
        order.status !== 'pending' && 
        !order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase order',
      error: error.message
    });
  }
});

// Create new purchase order (admin only)
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('Received order data:', req.body);
    console.log('User:', req.user);

    // Validate required fields
    const { client, title, material, pickupLocation, deliveryLocations, pickupDate, deliveryDate, weight, haulingRate } = req.body;

    if (!client || !title || !material || !pickupLocation || !deliveryLocations || !Array.isArray(deliveryLocations) ||
        deliveryLocations.length === 0 || !pickupDate || !deliveryDate || !weight || !haulingRate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        error: 'All required fields must be provided, including material type and at least one delivery location'
      });
    }

    // Transform the data to match new schema
    const orderData = {
      client: client.trim(),
      title: title.trim(),
      description: req.body.description?.trim() || '',
      material: material.trim(),
      pickupLocation: {
        address: pickupLocation,
        coordinates: { lat: 0, lng: 0 } // Will be set by pre-save middleware
      },
      deliveryLocations: deliveryLocations.map(address => ({
        address: address,
        coordinates: { lat: 0, lng: 0 }, // Will be set by pre-save middleware
        status: 'pending'
      })),
      pickupDate,
      deliveryDate,
      weight: parseFloat(weight),
      haulingRate: parseFloat(haulingRate),
      notes: req.body.notes?.trim() || '',
      createdBy: req.user._id
    };

    console.log('Creating order with data:', orderData);

    const order = new PurchaseOrder(orderData);
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');

    console.log('Order created successfully:', order);

    res.status(201).json({
      success: true,
      message: 'Purchase order created successfully',
      data: order
    });
  } catch (error) {
    console.error('Error creating purchase order:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating purchase order',
      error: error.message
    });
  }
});

// Accept purchase order (trucker only)
router.patch('/:id/accept', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const { latitude, longitude, developerMode } = req.body;

    // Validate location data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data (latitude and longitude) is required to accept an order'
      });
    }

    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    if (order.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Purchase order is not available for acceptance'
      });
    }

    // Check if trucker is within 1km of pickup location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinPickupGeofence(latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the pickup location to accept this order'
        });
      }
    }

    // Check if trucker already has an active order
    const existingActiveOrder = await PurchaseOrder.findOne({
      assignedTo: req.user._id,
      status: { $in: ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'] }
    });

    if (existingActiveOrder) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active order. Complete it before accepting a new one.'
      });
    }

    order.status = 'accepted';
    order.assignedTo = req.user._id;
    order.acceptedAt = new Date();

    await order.save();
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order accepted successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error accepting purchase order',
      error: error.message
    });
  }
});

// Update purchase order status (admin only)
router.patch('/:id/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { status } = req.body;
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    order.status = status;
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order status updated successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating purchase order status',
      error: error.message
    });
  }
});

// Complete pickup (trucker only)
router.patch('/:id/complete-pickup', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    if (!order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this order'
      });
    }

    if (order.status !== 'accepted') {
      return res.status(400).json({
        success: false,
        message: 'Order must be in accepted status to complete pickup'
      });
    }

    order.status = 'pickup_completed';
    order.pickupCompletedAt = new Date();

    await order.save();

    res.json({
      success: true,
      message: 'Pickup completed successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing pickup',
      error: error.message
    });
  }
});

// Complete delivery location (trucker only)
router.patch('/:id/complete-delivery/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const { latitude, longitude, deliveryPhoto, signature, developerMode } = req.body;

    // Validate required data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data is required'
      });
    }

    if (!deliveryPhoto || !signature) {
      return res.status(400).json({
        success: false,
        message: 'Delivery photo and signature are required'
      });
    }

    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    if (!order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this order'
      });
    }

    // Check if trucker is within geofence for this delivery location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinDeliveryGeofence(req.params.deliveryId, latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the delivery location to complete delivery'
        });
      }
    }

    // Find and update the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (deliveryLocation.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'This delivery location is already completed'
      });
    }

    // Update delivery location
    deliveryLocation.status = 'completed';
    deliveryLocation.completedAt = new Date();
    deliveryLocation.deliveryPhoto = deliveryPhoto;
    deliveryLocation.signature = signature;

    // Check if all deliveries are completed
    const allCompleted = order.deliveryLocations.every(loc => loc.status === 'completed');

    if (allCompleted) {
      order.status = 'delivered';
    } else {
      order.status = 'delivery_in_progress';
    }

    await order.save();

    res.json({
      success: true,
      message: allCompleted ? 'All deliveries completed! Order finished.' : 'Delivery location completed successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing delivery',
      error: error.message
    });
  }
});

// Delete all purchase orders (admin only - for testing)
router.delete('/admin/delete-all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await PurchaseOrder.deleteMany({});

    res.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} purchase orders`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting all purchase orders',
      error: error.message
    });
  }
});

// Delete purchase order (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const order = await PurchaseOrder.findByIdAndDelete(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    res.json({
      success: true,
      message: 'Purchase order deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting purchase order',
      error: error.message
    });
  }
});

export default router;
